package com.pass.hbl.manager.backend.persistence.service.hm.handlers;

import com.pass.hbl.manager.backend.persistence.exception.SchedulingException;
import com.pass.hbl.manager.backend.persistence.service.hm.HmSchedulerService;
import com.pass.hbl.manager.backend.persistence.service.hm.UserProfileService;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.UUID;

import static com.pass.hbl.manager.backend.persistence.util.Constants.ADD_SYSTEM_BID;
import static com.pass.hbl.manager.backend.persistence.util.Constants.END_TRANSFER_AUCTION_JOB;

@Slf4j
@Service
@Transactional
@Getter
@Setter
public class TransferMarketSchedulingHandler {

    private final HmSchedulerService schedulerService;

    private final UserProfileService userProfileService;

    public TransferMarketSchedulingHandler(HmSchedulerService schedulerService, UserProfileService userProfileService) {
        this.schedulerService = schedulerService;
        this.userProfileService = userProfileService;
    }

    public void addSchedules(Map<UUID, LocalDateTime> scheduleByIdMap, boolean isSystemPlayer, String jobName) throws SchedulingException {
        schedulerService.scheduleTransferMarketJobs(scheduleByIdMap, isSystemPlayer, jobName);
    }

    @Transactional(propagation = Propagation.MANDATORY)
    public void removeSystemUserSchedules(UUID transferMarketId) {
        log.info("removeSystemUserSchedules for transferMarketId ["+ transferMarketId + "] called" );
        schedulerService.removeTransferMarketSchedule(transferMarketId, END_TRANSFER_AUCTION_JOB);
        schedulerService.removeTransferMarketSchedule(transferMarketId, ADD_SYSTEM_BID);
    }

    @Transactional(propagation = Propagation.MANDATORY)
    public void removeAddSystemBidScheduleOfSystemUser(UUID transferMarketId) {
        schedulerService.removeTransferMarketSchedule(transferMarketId, ADD_SYSTEM_BID);
    }
}
