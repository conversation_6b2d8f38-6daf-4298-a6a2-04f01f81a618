package com.pass.hbl.manager.backend.persistence.service.hm;

import com.pass.hbl.manager.backend.persistence.domain.hm.*;
import com.pass.hbl.manager.backend.persistence.dto.admin.TransferMarketAdminDto;
import com.pass.hbl.manager.backend.persistence.dto.hm.*;
import com.pass.hbl.manager.backend.persistence.entity.hm.*;
import com.pass.hbl.manager.backend.persistence.exception.*;
import com.pass.hbl.manager.backend.persistence.mapper.hm.*;
import com.pass.hbl.manager.backend.persistence.repository.hm.HmTransferMarketBidRepository;
import com.pass.hbl.manager.backend.persistence.repository.hm.HmTransferMarketRepository;
import com.pass.hbl.manager.backend.persistence.repository.hm.HmUntradedTransferMarketPlayerRepository;
import com.pass.hbl.manager.backend.persistence.service.AbstractService;
import com.pass.hbl.manager.backend.persistence.service.admin.LogMessageService;
import com.pass.hbl.manager.backend.persistence.service.admin.ParameterService;
import com.pass.hbl.manager.backend.persistence.service.hm.handlers.*;
import com.pass.hbl.manager.backend.persistence.service.shared.TransactionHandler;
import com.pass.hbl.manager.backend.persistence.util.ParameterDefaults;
import com.pass.hbl.manager.backend.persistence.util.Util;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.event.EventListener;
import org.springframework.data.jpa.repository.Lock;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.LockModeType;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.pass.hbl.manager.backend.persistence.service.hm.helpers.TransferMarketHelper.getRandomAuctionDuration;
import static com.pass.hbl.manager.backend.persistence.service.hm.helpers.TransferMarketHelper.validateUserIsTransferItemOwner;
import static com.pass.hbl.manager.backend.persistence.util.Constants.*;
import static com.pass.hbl.manager.backend.persistence.util.ParameterDefaults.*;
import static java.lang.Double.valueOf;
import static java.time.LocalDateTime.now;
import static java.util.Collections.emptyList;
import static java.util.Objects.nonNull;
import static java.util.UUID.fromString;

@Slf4j
@Service
@Transactional
public class TransferMarketService extends AbstractService<HmTransferMarket, TransferMarketDto> {

    public static final String USER_ID = "USER_ID";
    private final Random RAND = new Random();

    private final HmTransferMarketRepository transferMarketRepository;
    private final HmTransferMarketBidRepository transferMarketBidRepository;
    private final HmUntradedTransferMarketPlayerRepository untradedTransferMarketPlayerRepository;

    private final UserProfileService userProfileService;
    private final TeamService teamService;
    private final LeagueService leagueService;
    private final PlayerService playerService;
    private final LogMessageService logMessageService;
    private final SeasonService seasonService;

    private final HmTransferMarketMapper transferMarketMapper;
    private final HmTransferMarketHistoryMapper transferMarketHistoryMapper;
    private final HmTransferMarketUserHistoryMapper hmTransferMarketUserHistoryMapper;
    private final HmTransferMarketBidMapper transferMarketBidMapper;
    private final HmBidMapper bidMapper;
    private final TransferMarketBidHandler transferMarketBidHandler;
    private final TransferMarketSystemPlayerHandler systemPlayerHandler;
    private final TransferMarketSchedulingHandler schedulingHandler;
    private final TransactionHandler transactionHandler;
    private final TransferMarketInfoHandler transferMarketInfoHandler;
    private final ConsistencyCheckHandler consistencyCheckHandler;
    private final TransferMarketAdminHandler transferMarketAdminHandler;

    private final ParameterService parameterService;

    private int maxTeamSize = ParameterDefaults.DEFAULT_INITIAL_TEAM_SIZE;

    @Getter
    private Boolean isTransferMarketActive = Boolean.valueOf(DEFAULT_TRANSFER_MARKET_ACTIVE);

    @Getter
    private TransferMarketInactiveReason transferMarketInactiveReason = TransferMarketInactiveReason.getByStringCaseIgnore(DEFAULT_TRANSFER_MARKET_INACTIVE_REASON);

    public TransferMarketService(HmTransferMarketRepository transferMarketRepository,
                                 HmTransferMarketBidRepository transferMarketBidRepository,
                                 HmUntradedTransferMarketPlayerRepository untradedTransferMarketPlayerRepository, UserProfileService userProfileService,
                                 TeamService teamService,
                                 @Lazy LeagueService leagueService,
                                 @Lazy PlayerService playerService,
                                 LogMessageService logMessageService,
                                 @Lazy SeasonService seasonService, HmTransferMarketMapper transferMarketMapper,
                                 HmTransferMarketHistoryMapper transferMarketHistoryMapper, HmTransferMarketUserHistoryMapper hmTransferMarketUserHistoryMapper, HmTransferMarketBidMapper transferMarketBidMapper,
                                 HmBidMapper bidMapper,
                                 TransferMarketBidHandler transferMarketBidHandler,
                                 TransferMarketSystemPlayerHandler systemPlayerHandler,
                                 TransferMarketSchedulingHandler transferMarketSchedulingHandler,
                                 TransactionHandler transactionHandler,
                                 @Lazy TransferMarketInfoHandler transferMarketInfoHandler, ConsistencyCheckHandler consistencyCheckHandler, TransferMarketAdminHandler transferMarketAdminHandler, ParameterService parameterService) throws InvalidOperationException, FormatException {
        super(transferMarketRepository, transferMarketMapper, HmTransferMarket.class);
        this.transferMarketRepository = transferMarketRepository;
        this.transferMarketBidRepository = transferMarketBidRepository;
        this.untradedTransferMarketPlayerRepository = untradedTransferMarketPlayerRepository;
        this.userProfileService = userProfileService;
        this.teamService = teamService;
        this.leagueService = leagueService;
        this.playerService = playerService;
        this.logMessageService = logMessageService;
        this.seasonService = seasonService;
        this.transferMarketMapper = transferMarketMapper;
        this.transferMarketHistoryMapper = transferMarketHistoryMapper;
        this.hmTransferMarketUserHistoryMapper = hmTransferMarketUserHistoryMapper;
        this.transferMarketBidMapper = transferMarketBidMapper;
        this.bidMapper = bidMapper;
        this.transferMarketBidHandler = transferMarketBidHandler;
        this.systemPlayerHandler = systemPlayerHandler;
        this.schedulingHandler = transferMarketSchedulingHandler;
        this.transactionHandler = transactionHandler;
        this.transferMarketInfoHandler = transferMarketInfoHandler;
        this.consistencyCheckHandler = consistencyCheckHandler;
        this.transferMarketAdminHandler = transferMarketAdminHandler;
        this.parameterService = parameterService;

    }

    @EventListener(ApplicationReadyEvent.class)
    public void init() throws InvalidOperationException, FormatException {
        maxTeamSize = parameterService.getAsInteger(ParameterDefaults.PARAM_INITIAL_TEAM_SIZE, ParameterDefaults.DEFAULT_INITIAL_TEAM_SIZE, userProfileService.getSystemUserName());
        isTransferMarketActive = parameterService.getAsBoolean(PARAM_TRANSFER_MARKET_ACTIVE, Boolean.valueOf(DEFAULT_TRANSFER_MARKET_ACTIVE), SYSTEM_USER);
        try {
            if (!isTransferMarketActive) {
                transferMarketInactiveReason = TransferMarketInactiveReason.getByStringCaseIgnore(parameterService.getValue(PARAM_TRANSFER_MARKET_INACTIVE_REASON, DEFAULT_TRANSFER_MARKET_INACTIVE_REASON, SYSTEM_USER));
            }
        } catch (Exception e) {
            log.error("Failed get get transferMarketInactiveReason", e);
            transferMarketInactiveReason = TransferMarketInactiveReason.DEFAULT;
        }
    }

    public void drawInitialTransferMarket(@NotNull HmUserProfile user, @NotNull HmLeague league, List<HmPlayer> excludedPlayers) throws InvalidOperationException, FormatException, SchedulingException, EntityNotExistException {
        List<Pair<HmPlayer, Integer>> selectedPlayers = teamService.getInitialTransferMarketTeam(user, league, excludedPlayers);
        // now when the full players were picked, create the transfer market objects and save them
        HmUserProfile systemUser = userProfileService.getSystemUser();
        //Create system transfer market entities
        List<HmTransferMarket> transferMarketList = new ArrayList<>();
        for (int i = 0; i < selectedPlayers.size(); i++) {
            HmPlayer p = selectedPlayers.get(i).getKey();
            // Insert a difference of a minute to avoid threads concurrency
            transferMarketList.add(new HmTransferMarket(systemUser,
                    league, p, new ArrayList<>(), selectedPlayers.get(i).getValue(), LocalDateTime.now().plusMinutes(i).plusHours(getRandomAuctionDuration()).minusSeconds(RAND.nextInt(0, 61))));
        }
        addTransferMarketItems(transferMarketList);
    }

    private void addTransferMarketItems(List<HmTransferMarket> transferMarketList) throws SchedulingException {
        transferMarketRepository.saveAll(transferMarketList);
        //Schedule transfer market auction ends
        Map<UUID, LocalDateTime> auctionEndByIdMap = new HashMap<>();
        transferMarketList.forEach(t -> auctionEndByIdMap.put(t.getId(), t.getAuctionEnd()));
        schedulingHandler.addSchedules(auctionEndByIdMap, true, END_TRANSFER_AUCTION_JOB);
    }

    @Transactional(propagation = Propagation.REQUIRED)
    @Retryable(value = {RuntimeException.class}, backoff = @Backoff(delay = 1000))
    public void fixInitialTransferMarket(UUID leagueId, List<HmPlayerDO> allValidPlayers) {

        Triple<List<UUID>, List<Position>, List<Integer>> systemPlayerInfoInLeague = systemPlayerHandler.getSystemPlayerInfoInLeague(leagueId);
        List<Position> existingSystemPlayerPositions = systemPlayerInfoInLeague.getMiddle();
        if (existingSystemPlayerPositions.size() >= maxTeamSize) {
            log.info("ConsistencyCheck: league " + leagueId + " has " + existingSystemPlayerPositions.size() + " system players. No need to fix");
            return;
        }

        try {
            log.info("ConsistencyCheck: Fixing missing system transfer items for league id[" + leagueId + "]:" + (maxTeamSize - existingSystemPlayerPositions.size()) + " items required.");
            HmUserProfile systemUser = userProfileService.getSystemUser();
            HmLeague league = leagueService.getById(leagueId);
            List<HmPlayer> selectedPlayers = teamService.getMissingTransferMarketPlayers(systemUser, league, existingSystemPlayerPositions, systemPlayerInfoInLeague.getRight(), allValidPlayers);
            //Create system transfer market entities
            fillTransferMarket(league, selectedPlayers, systemUser, systemPlayerInfoInLeague.getLeft());
        } catch (Exception e) {
            String msg = "Failed to fix missing system transfer items for league id[" + leagueId + "]. Reason: " + e.getMessage();
            log.error(msg, e);
            logMessageService.logException("TransferMarketService::fixInitialTransferMarket", msg, e);
        }
    }

    private void fillTransferMarket(HmLeague league, List<HmPlayer> selectedPlayers, HmUserProfile systemUser, List<UUID> existingPlayerIds) throws SchedulingException {
        long start = System.currentTimeMillis();
        log.info("ConsistencyCheck: fillTransferMarket, existing player ids: " + existingPlayerIds + " in league: " + league.getId());
        List<HmTransferMarket> transferMarketList = new ArrayList<>();
        log.info("ConsistencyCheck: fillTransferMarket, selected player ids: " + selectedPlayers.stream().map(HmPlayer::getId).toList() + " in league: " + league.getId());
        for (int i = 0; i < selectedPlayers.size(); i++) {
            HmPlayer p = selectedPlayers.get(i);
            // Insert a difference of a minute to avoid threads concurrency
            if (!existingPlayerIds.contains(p.getId())) {
                transferMarketList.add(new HmTransferMarket(systemUser,
                        league, p, new ArrayList<>(), p.getMarketValue(), LocalDateTime.now().plusMinutes(i).plusHours(getRandomAuctionDuration()).minusSeconds(RAND.nextInt(0, 61))));
            } else {
                log.info("ConsistencyCheck: fillTransferMarket, player with id[" + p.getId()+ "] already exists in TM of league: " + league.getId() +". Skipping..");
            }
        }
        addTransferMarketItems(transferMarketList);
    }

    @Transactional(propagation = Propagation.REQUIRED)
    public boolean exists(String transferMarketId) throws FormatException {
        return transferMarketRepository.existsById(Util.convertId(transferMarketId));
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW, readOnly = true)
    public boolean istTransferItemInCurrentSeason(UUID leagueSeasonId) throws EntityNotExistException {
        UUID currentSeasonId = seasonService.getCurrentSeason().getId();
        return Objects.nonNull(leagueSeasonId) && Objects.equals(currentSeasonId, leagueSeasonId);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW, readOnly = true)
    public Optional<HmLeagueSeasonInfoDO> getLeagueSeasonInfoByTransferItem(String transferMarketId) throws FormatException {
        return transferMarketRepository.findLeagueSeasonInfoByTransferMarketId(Util.convertId(transferMarketId));
    }

    @Transactional(propagation = Propagation.REQUIRED)
    public void endAuctionAsAdmin(String transferMarketId) throws EntityNotExistException, FormatException {
        Optional<HmLeagueSeasonInfoDO> leagueSeasonInfoOptional = transactionHandler.runInNewTransactionReadOnly(() -> {
            try {
                return getLeagueSeasonInfoByTransferItem(transferMarketId);
            } catch (Exception e) {
               return Optional.empty();
            }
        });
        if (leagueSeasonInfoOptional.isEmpty()) {
            log.info("LeagueSeasonInfoDo of transfer market id [" + transferMarketId + "] not found. It will be deleted and not rescheduled.");
            deleteById(UUID.fromString(transferMarketId));
            return;
        }
        HmLeagueSeasonInfoDO leagueSeasonInfoDO = leagueSeasonInfoOptional.get();
        endAuction(transferMarketId, Collections.emptySet(), leagueSeasonInfoDO.getIsActive());
    }

    @Transactional(propagation = Propagation.REQUIRED)
    public void endAuction(String transferMarketId, boolean isLeagueActive, UUID seasonId) throws EntityNotExistException, FormatException {
        endAuction(transferMarketId, Collections.emptySet(), isLeagueActive);
    }

    @Transactional(propagation = Propagation.REQUIRED)
    public UUID endAuction(String transferMarketId, Set<UUID> ignoredPlayerIds, boolean isLeagueActive) throws EntityNotExistException, FormatException {
        TransferMarketInfoDo transferMarketInfo = transferMarketInfoHandler.getTransferMarketInfoDo(Util.convertId(transferMarketId));
        List<TransferMarketBidInfoDo> bidInfoList = transferMarketInfoHandler.getBidInfoListByOfferId(Util.convertId(transferMarketId));

        // due to what ever reason, we do not have a transfer item. So just return
        if(!transferMarketRepository.existsById(Util.convertId(transferMarketId))) {
            String msg = "Could not find transfer market object with id=" + transferMarketId + ". Please check consistency. Cannot end auction.";
            log.warn(msg);
            logMessageService.logWarning("TransferMarketService::endAuction", msg, null);
            return null;
        }

        // should not happen, but in case the owner of the item already got deleted, just delete this item and do nothing
        if (!userProfileService.exists(transferMarketInfo.ownerId())) {
            transferMarketBidRepository.deleteAllByOfferId(transferMarketInfo.id());
            transferMarketRepository.deleteById(transferMarketInfo.id());
            String msg = "Owner of transfer market object with id=" + transferMarketInfo.id() + " seams to be deleted. Cannot end auction, just delete it.";
            log.warn(msg);
            logMessageService.logWarning("TransferMarketService::endAuction", msg, null);
            return null;
        }

        // should also not happen, but in case a bidder is a deleted user, just delete the bid
        List<UUID> invalidBids = bidInfoList.stream()
                .filter(bid -> !userProfileService.exists(bid.bidderId())).map(TransferMarketBidInfoDo::id).toList();
        if (CollectionUtils.isNotEmpty(invalidBids)) {
            transferMarketBidRepository.deleteAllById(invalidBids);
            String msg = "TransferItem " + transferMarketId + " had bids of deleted users. Removing them before processing.";
            log.warn(msg);
            logMessageService.logWarning("TransferMarketService::endAuction", msg, null);
        }

        // re-get object to have a clean state
        transferMarketInfo = transferMarketInfoHandler.getTransferMarketInfoDo(Util.convertId(transferMarketId));
        Pair<UUID, HmTransferMarket> newEntry = null;

        // if TM item is a system player auction then we need to do actions
        boolean isStart7Seller = userProfileService.isSystemUser(transferMarketInfo.ownerId());
        if (isStart7Seller) {
            // If league is inactive, the current system transfer market will not be rescheduled
            if (!isLeagueActive) {
                log.info("League of transfer market id [" + transferMarketId + "] is inactive. It will be ended but not rescheduled.");
            }

            long systemPlayersOnMarket = transferMarketRepository.countByLeagueIdAndOwnerId(transferMarketInfo.leagueId(), userProfileService.getSystemUserId());

            boolean overload = systemPlayersOnMarket > maxTeamSize;

            bidInfoList = transferMarketInfoHandler.getBidInfoListByOfferId(Util.convertId(transferMarketId));

            try {
                // we make a deal
                if (!bidInfoList.isEmpty()) {

                    Optional<UUID> bestBid = getValidPlayerBid(bidInfoList, transferMarketInfo.price());
                    if (bestBid.isPresent()) {

                        // send accept bid notification occurs only if accept bid and replace player are committed
                        AcceptBidNotificationDo bidNotificationDo = transferMarketBidHandler.acceptTransferMarketBid(transferMarketInfo.ownerId(), bestBid.get(), false);
                        if (nonNull(bidNotificationDo)) {
                            transferMarketBidHandler.sendAcceptBidNotifications(bidNotificationDo);
                        }

                    // reject the offer, no deal
                    } else {
                        for (TransferMarketBidInfoDo bid : bidInfoList) {
                            transferMarketBidHandler.rejectBidBySystem(bid, transferMarketInfo.playerId(), transferMarketInfo.leagueId());
                        }
                    }
                // Start7 is seller, but no one bids to the player
                }
                //else {
                    // Temporary deactivated since it is not used in PlayerMarketValueJob
                    //untradedTransferMarketPlayerRepository.save(new HmUntradedTransferMarketPlayer(leagueService.getByIdInNewTransaction(transferMarketInfo.leagueId()),
                    //        playerService.getByIdInNewTransaction(transferMarketInfo.playerId()), Double.valueOf(transferMarketInfo.price() * 0.95).intValue()));
                //}

                if (!overload && isLeagueActive) {
                    newEntry = systemPlayerHandler.createNewTransferMarketEntry(transferMarketInfo.leagueId(), transferMarketInfo.playerId(), ignoredPlayerIds);
                }
            } catch (Exception e) {
                String message = "Failed to end system player auction id " + transferMarketInfo.id() + " having  "+ bidInfoList.size()+" bids. Cleaning up and potentially waiting for consistency check";
                logMessageService.logException("TransferMarketService", message, e);
                log.error(message, e);
            }
        }

        // on end auction all deals are made. Delete all entities
        transferMarketRepository.deleteById(transferMarketInfo.id());
        transferMarketBidRepository.deleteAllByOfferId(transferMarketInfo.id());

        // Removing schedules is not necessary since the job is currently running and will be removed at the end
        /*if (isStart7Seller) {
            schedulingHandler.removeSystemUserSchedules(transferMarketInfo.id());
        } else {
            schedulingHandler.removeAddSystemBidScheduleOfSystemUser(transferMarketInfo.id());
        }
        */
        if (newEntry != null && newEntry.getRight() != null) {
            try {
                HmTransferMarket newTransferMarketEntry = newEntry.getRight();
                transferMarketRepository.save(newTransferMarketEntry);
                schedulingHandler.addSchedules(Map.of(newTransferMarketEntry.getId(), newTransferMarketEntry.getAuctionEnd()), true, END_TRANSFER_AUCTION_JOB);
                return newEntry.getLeft();
            } catch (SchedulingException e) {
                String message = "Failed to save new transfer market item, waiting for consistency check";
                logMessageService.logException("TransferMarketService", message, e);
                log.error(message, e);
            }
        }
        return null;

    }

    private Optional<UUID> getValidPlayerBid(List<TransferMarketBidInfoDo> bids, @NotNull int requestedPriceByStart7) throws CouldNotHappenException {
        if (CollectionUtils.isEmpty(bids)) {
            return Optional.empty();
        }

        // the highest bid wins. If two bids have the same value the first bidder wins
        TransferMarketBidInfoDo highestAndOldestBid = bids.stream()
                .min(Comparator.comparing(TransferMarketBidInfoDo ::bid).reversed().thenComparing(TransferMarketBidInfoDo::createdAt))
                .orElseThrow(CouldNotHappenException::new);

        // we only accept bids if they are at least 90% of the requested value
        if (highestAndOldestBid.bid() < (requestedPriceByStart7 * 0.9)) {
            return Optional.empty();
        }
        return Optional.of(highestAndOldestBid.id());
    }

    @Lock(LockModeType.READ)
    @Transactional(readOnly = true)
    public List<TransferMarketDto> getByLeagueId(@NotNull String userId, @NotNull String leagueId, boolean userInclusive) throws EntityNotExistException, FormatException {
        HmLeague league = leagueService.getById(leagueId);
        Iterable<HmTransferMarket> transferMarkets = transferMarketRepository.findByLeagueId(league.getId());
        if (!userInclusive)
            return transferMarketMapper.mapToDto(Util.toStream(transferMarkets).filter(t -> !t.getOwner().getId().toString().equals(userId)).toList());
        return transferMarketMapper.mapToDto(transferMarkets);
    }

    @Lock(LockModeType.READ)
    @Transactional(readOnly = true)
    public List<TransferMarketLobbyDto> getNewTransfersByLeagueIdUserExclusive(@NotNull String userId, @NotNull UUID leagueId, int countNewTransfers) {
        List<HmTransferMarketLobbyDO> newTransfersByLeague = transferMarketRepository.findNewTransfersByLeagueId(leagueId, countNewTransfers);
        Set<String> ownerIds = newTransfersByLeague.stream().map(HmTransferMarketLobbyDO::getOwnerId).filter(ownerId -> !Objects.equals(ownerId, userId)).collect(Collectors.toSet());
        // make sure that the owners of the transfers are not deleted
        Set<UUID> existingOwners = userProfileService.getExistingUsersFromList(ownerIds);
        return newTransfersByLeague.stream().
                filter(tlDO -> !Objects.equals(tlDO.getOwnerId(), userId) && existingOwners.contains(fromString(tlDO.getOwnerId())))
                .map(tlDO -> {
                            try {
                                return new TransferMarketLobbyDto(tlDO.getId(), playerService.getPlayerLobbyDto(fromString(tlDO.getPlayerId())),tlDO.getPrice(), Util.toZonedDateTime(tlDO.getCreatedAt()));
                            } catch (Exception e) {
                                return null;
                            }
                        }).filter(Objects::nonNull).toList();
    }

    /*
     * Relevant for local tests
     */
    public TransferMarketDto getRandomByLeagueId(String leagueId) throws EntityNotExistException, FormatException {
        HmLeague league = leagueService.getById(leagueId);
        List<HmTransferMarket> systemPlayerTransfers = systemPlayerHandler.getSystemTransferItems(league.getId());
        return transferMarketMapper.mapToDto(systemPlayerTransfers.get(RAND.nextInt(0, systemPlayerTransfers.size() - 1)));
    }

    @Transactional(readOnly = true, propagation = Propagation.REQUIRES_NEW)
    public List<HmTransferMarketDO> getSystemTransferItemsAuctionEndBefore(UUID leagueId, LocalDateTime auctionEnd) {
        return  systemPlayerHandler.getSystemTransferItemsAuctionEndBefore(leagueId, auctionEnd);
    }

    @Transactional(propagation = Propagation.REQUIRED)
    public TransferMarketDto addPlayerTransferItem(@NotNull String userId, @NotNull TransferMarketRequestDto transferMarketRequestDto) throws EntityNotExistException, FormatException, InvalidOperationException, SchedulingException {

        HmLeague league = leagueService.getByIdInNewTransaction(transferMarketRequestDto.getLeagueId());
        HmPlayer player = playerService.getByIdInNewTransaction(transferMarketRequestDto.getPlayerId());

        HmUserProfile currentUser = userProfileService.getByIdInNewTransaction(userId);
        UUID playerId = player.getId();
        UUID leagueId = league.getId();
        if (!teamService.isPlayerOwnedByUser(currentUser, league, playerId)) {
            throw new InvalidOperationException("Add player to transfer market", userId, "Player is not owned by current user");
        }

        if (existsInLatestLineup(currentUser, leagueId, playerId)) {
            throw new InvalidOperationException("Add player to transfer market", userId, "Player already exists in current lineup");
        }

        transferMarketRepository.findByLeagueIdAndPlayerId(leagueId, playerId).ifPresent(item -> deleteTransferItem(item.getId(),false));

        int hours = AuctionDuration.valueOf(transferMarketRequestDto.getDuration()).getHours();
        HmTransferMarket transferMarket = new HmTransferMarket(currentUser,
                league, player, new ArrayList<>(), transferMarketRequestDto.getPrice(), LocalDateTime.now().plusHours(hours));
        HmTransferMarket savedItem = transferMarketRepository.save(transferMarket);
        // schedule auction end
        schedulingHandler.addSchedules(Map.of(transferMarket.getId(), transferMarket.getAuctionEnd()), false, END_TRANSFER_AUCTION_JOB);
        // schedule add system bid job: minimum on hour after transfer item creation and maximum two hours after the auction end (upper bound exclusive)
        schedulingHandler.addSchedules(Map.of(transferMarket.getId(), LocalDateTime.now().plusHours(RAND.nextInt(1, hours - 1))), false, ADD_SYSTEM_BID);

        UserDto ownerDto = transactionHandler.runInNewTransactionReadOnly(() -> userProfileService.mapToReducedDto(currentUser));
        PlayerDto playerDto = playerService.getByIdAsDtoInNewTransaction(playerId.toString());

        return new TransferMarketDto(savedItem.getId().toString(), ownerDto,
                leagueId.toString(),playerDto, Collections.emptyList(),
                savedItem.getPrice(),
                ZonedDateTime.of(savedItem.getAuctionEnd(), TimeZone.getDefault().toZoneId()),
                null,  ZonedDateTime.of(savedItem.getCreatedAt(), TimeZone.getDefault().toZoneId()));
    }

    public void removePlayerTransferItem(@NotNull String userId, @NotNull String id) throws InvalidOperationException, FormatException {
        UUID transferMarketId = Util.convertId(id);
        if (!transferMarketRepository.existsById(transferMarketId)) {
            return;
        }

        Optional<UUID> ownerId = transferMarketRepository.findOwnerId(transferMarketId);
        // do not validate ownership for system user or deleted users
        if (!userId.equals(userProfileService.getSystemUserId().toString()) && ownerId.isPresent()) {
            validateUserIsTransferItemOwner(
                    "Remove player from transfer market id[" + id + "]", userId, ownerId.get().toString(), "The user ist not authorized to remove a bid of another manager");
        }
        deleteTransferItem(transferMarketId,true);
    }

    public void addSystemBid(String transferMarketId) throws EntityNotExistException, InvalidOperationException, FormatException {
        transferMarketBidHandler.addSystemBid(transferMarketId);
    }

    public TransferMarketBidDto addTransferItemBid(String userId, String transferMarketId, int value) throws EntityNotExistException, FormatException, InvalidOperationException {
        return transferMarketBidHandler.addTransferItemBid(userId, transferMarketId, value);
    }

    public TransferMarketBidDto updateTransferItemBid(String userId, String bidId, int value) throws EntityNotExistException, FormatException, InvalidOperationException {
        return transferMarketBidHandler.updateTransferItemBid(userId, bidId, value);
    }

    public void acceptTransferItemBid(String userId, String bidId) throws EntityNotExistException, FormatException, InvalidOperationException {
        AcceptBidNotificationDo bidNotificationDo = transferMarketBidHandler.acceptTransferMarketBid(Util.convertId(userId), Util.convertId(bidId), true);
        if (nonNull(bidNotificationDo)) {
            transferMarketBidHandler.sendAcceptBidNotifications(bidNotificationDo);
        }
    }

    public void removeTransferItemBid(String userId, String id) throws FormatException, InvalidOperationException, EntityNotExistException {
        transferMarketBidHandler.removeTransferItemBid(userId, id);
    }

    public TransferMarketBidDto rejectTransferItemBid(String userId, String id) throws EntityNotExistException, FormatException, InvalidOperationException {
        return transferMarketBidHandler.rejectTransferItemBid(userId, id);
    }

    public void deleteAppearancesAndBidsByLeague(UUID leagueId) {
        for (UUID id : Util.toStream(transferMarketRepository.findByLeagueId(leagueId)).map(HmTransferMarket::getId).toList())
            deleteTransferItem(id,false);
    }

    public void deleteAppearancesAndBidsByLeagueAndOwner(UUID leagueId, UUID ownerId) {
        for (UUID id : Util.toStream(transferMarketRepository.findByLeagueIdAndOwnerId(leagueId, ownerId)).map(HmTransferMarket::getId).toList())
            deleteTransferItem(id,false);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void deleteById(@NotNull UUID transferMarketId) {
        transferMarketBidRepository.deleteAllByOfferId(transferMarketId);
        transferMarketRepository.deleteById(transferMarketId);
    }

    public void deleteBidsByLeagueAndBidder(UUID leagueId, UUID bidderId) {
        for (UUID id : Util.toStream(transferMarketBidRepository.findByOfferLeagueIdAndBidderId(leagueId, bidderId)).map(HmTransferMarketBid::getId).toList())
            transferMarketBidRepository.deleteById(id);
    }

    public void deleteAppearancesAndBidsByOwner(UUID ownerId) {
        for (UUID id : Util.toStream(transferMarketRepository.findByOwnerId(ownerId)).map(HmTransferMarket::getId).toList())
            deleteTransferItem(id,false);
        transferMarketBidRepository.deleteAllByBidderId(ownerId);
    }

    public List<TransferMarketBidDto> getAllBidsByUser(String userId) throws FormatException {
        Iterable<HmTransferMarketBid> bids = transferMarketBidRepository.findByOfferOwnerId(Util.convertId(userId));
        List<HmTransferMarketBid> sortedBids = new ArrayList<>(Util.toStream(bids).toList());
        sortedBids.sort(Comparator.comparing(HmTransferMarketBid::getCreatedAt).reversed());
        return transferMarketBidMapper.mapToDto(sortedBids);
    }

    private boolean existsInLatestLineup(HmUserProfile user, UUID leagueId, UUID playerId) throws EntityNotExistException, FormatException {
        return teamService.isPlayerInLatestLineup(user, leagueId.toString(), playerId);
    }

    private void deleteTransferItem(UUID transferMarketId, boolean rejected) {
        if (transferMarketRepository.existsById(transferMarketId)) {
            transferMarketRepository.deleteById(transferMarketId);
        }
        if (transferMarketBidRepository.existsByOfferId(transferMarketId)) {
            if (rejected) {
                transferMarketBidRepository.deleteAndRejectAllByOfferId(transferMarketId);

            } else {
                transferMarketBidRepository.deleteAllByOfferId(transferMarketId);
            }
        }
        schedulingHandler.removeSystemUserSchedules(transferMarketId);
    }

    @Transactional(readOnly = true)
    public TransferMarketDto getEndedTransfer(String id) throws FormatException {
        UUID transferId = Util.convertId(id);
        Optional<HmTransferMarket> transferMarketOptional = transferMarketRepository.findEndedTransferById(transferId);
        TransferMarketDto transferMarketDto = transferMarketOptional.map(transferMarketMapper::mapToDto).orElse(null);
        if (nonNull(transferMarketDto)) transferMarketDto.setBids(bidMapper.mapToDto(transferMarketBidRepository.findClosedBidsByTransferId(transferId)));
        return transferMarketDto;
    }

    @Transactional(readOnly = true)
    public List<TransferMarketBidUserDto> getAllEndedTransferByLeagueAndUser(String leagueId, String userId) throws FormatException {
        List<TransferMarketBidUserDo> transferMarketBidUserResponse = transferMarketBidRepository.findEndedTransferByLeagueAndUser(Util.convertId(leagueId), Util.convertId(userId));
        Map<String, Object> context = new HashMap<>();
        context.put(USER_ID, userId);
        return hmTransferMarketUserHistoryMapper.mapToDto(transferMarketBidUserResponse, context).stream().filter(dto -> nonNull(dto.getTransferStatus())).toList();
    }

    @Lock(LockModeType.READ)
    @Transactional(readOnly = true)
    public List<TransferMarketDto> getHistoryByLeagueId(String leagueId, int count) throws EntityNotExistException, FormatException {
        HmLeague league = leagueService.getById(leagueId);
        Iterable<HmTransferMarket> transferMarkets = transferMarketRepository.findLastTransfersByLeagueId(league.getId(), count);
        List<TransferMarketDto> transferMarketDtos = transferMarketMapper.mapToDto(transferMarkets);
        transferMarketDtos.forEach(t -> t.setBids(bidMapper.mapToDto(transferMarketBidRepository.findClosedBidsByTransferId(fromString(t.getId())))));
        return transferMarketDtos;
    }

    @Lock(LockModeType.READ)
    @Transactional(readOnly = true)
    public List<TransferMarketHistoryDto> getHistoryByLeagueId(UUID leagueId, int count) {
        Iterable<HmTransferMarket> transferMarkets = transferMarketRepository.findLastTransfersByLeagueId(leagueId, count);
        List<TransferMarketHistoryDto> transferMarketDtos = transferMarketHistoryMapper.mapToDto(transferMarkets);
        transferMarketDtos.forEach(t -> t.setBids(bidMapper.mapToDto(transferMarketBidRepository.findClosedBidsByTransferId(fromString(t.getId())))));
        return transferMarketDtos;
    }

    @Lock(LockModeType.READ)
    @Transactional(readOnly = true, propagation = Propagation.REQUIRES_NEW)
    public Integer getAveragePlayerTransferValuesInDateInterval(UUID playerId, LocalDateTime from, LocalDateTime to) {
        List<HmEndedTransferDO> transferMarkets = Util.toStream(transferMarketRepository.findEndedPlayerTransfersInDateInterval(playerId, from, to)).toList();
        //List<HmUntradedTransferMarketPlayer> untradedPlayers = getUntradedByPlayerInDateInterval(playerId, from, to);
        Map<String, List<HmEndedTransferDO>> transferMarketsByLeague = transferMarkets.stream()
                .collect(Collectors.groupingBy(HmEndedTransferDO::getLeagueId,
                        Collectors.collectingAndThen(Collectors.toList(), list -> {
                            list.sort(Comparator.comparing(HmEndedTransferDO::getDeletedAt)
                            );
                            //get only the two first transfers
                            return list.size() > 2 ? list.subList(0, 2) : list;
                        })
                ));
        //IntStream untradedPlayerIntStream = untradedPlayers.stream().mapToInt(HmUntradedTransferMarketPlayer::getPrice);
        IntStream transferInStream = transferMarketsByLeague.values().stream().flatMap(Collection::stream).mapToInt(this::getFinalTransferValue);
        OptionalDouble average = transferInStream.average(); //IntStream.concat(transferInStream, untradedPlayerIntStream).average();
        return average.isPresent() ? valueOf(average.getAsDouble()).intValue() : null;
    }

    public List<HmUntradedTransferMarketPlayer> getUntradedByPlayerInDateInterval(UUID playerId, LocalDateTime from, LocalDateTime to) {
        return untradedTransferMarketPlayerRepository.findByPlayerIdAndCreatedAtAfterAndCreatedAtBefore(playerId, from, to);
    }

    private int getFinalTransferValue(HmEndedTransferDO transferMarket) {
        Iterable<HmTransferMarketBid> closedBidsByTransferId = transferMarketBidRepository.findClosedBidsByTransferId(UUID.fromString(transferMarket.getId()));
        Optional<HmTransferMarketBid> acceptedBid = Util.toStream(closedBidsByTransferId).filter(bid -> bid.getStatus().equals(BidStatus.ACCEPTED)).findFirst();
        return acceptedBid.map(HmTransferMarketBid::getBid).orElse(0);
    }

    @Transactional(readOnly = true)
    public List<HmLeagueExpiredTransfersDO> getAllExpiredSystemsTransfersByLeagueInfo(List<UUID> leagueIds) {
        UUID systemUserId = userProfileService.getSystemUserId();
        return transferMarketRepository.findLeagueExpiredTransfersInfoList(leagueIds, systemUserId);
    }

    public void checkTransferMarketByLeague(UUID leagueId) throws InvalidOperationException, FormatException, EntityNotExistException {
        HmLeague league = leagueService.getByIdInNewTransaction(leagueId);
        if (!league.isActive()) {
            // activate the league
            int rowsUpdated = leagueService.setLeagueActiveState(leagueId, true);
            if (rowsUpdated == 0) {
                log.info("checkTransferMarketByLeague:setLeagueActiveState league [" + leagueId + "] is already active. Skipping ..");
            } else {
                log.info("checkTransferMarketByLeague:setLeagueActiveState league [" + leagueId + "] activated");

                // find all valid players to be added to the transfer market
                int requiredFreePlayers = parameterService.getAsInteger(ParameterDefaults.PARAM_MIN_FREE_PLAYERS_ON_MARKET,
                        ParameterDefaults.DEFAULT_MIN_FREE_PLAYERS_ON_MARKET, userProfileService.getSystemUserName());
                List<HmPlayerDO> allValidPlayers = playerService.getAllValidPlayers();
                log.info("checkTransferMarketByLeague: all valid players count=" + allValidPlayers.size());
                consistencyCheckHandler.checkTransferMarketByLeague(requiredFreePlayers, allValidPlayers, leagueId, true);
            }
        }
    }

    @Lock(LockModeType.READ)
    @Transactional(readOnly = true)
    public List<HmTransferMarketTransactionDO> getPlayerPurchasesSinceDate(UUID userId, UUID leagueId, LocalDateTime sinceDate) {
        return transferMarketRepository.findPurchasesByUserAndLeagueSinceDate(userId, leagueId, sinceDate);
    }

    @Lock(LockModeType.READ)
    @Transactional(readOnly = true)
    public List<HmTransferMarketTransactionDO> getPlayerSalesSinceDate(UUID userId, UUID leagueId, LocalDateTime sinceDate) {
        return transferMarketRepository.findSalesByUserAndLeagueSinceDate(userId, leagueId, sinceDate);
    }

    @Lock(LockModeType.READ)
    @Transactional(readOnly = true)
    public List<TransferMarketAdminDto> getHistoryByLeagueAndUserAndPlayer(UUID playerId, String leagueName, String usernameOrEmailAddress, Integer count) throws EntityNotExistException {
        return transferMarketAdminHandler.getHistoryByLeagueAndUserAndPlayer(playerId, leagueName, usernameOrEmailAddress, count);
    }
}
